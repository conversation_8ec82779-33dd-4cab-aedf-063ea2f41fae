package model

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTaskCategoryName(t *testing.T) {
	t.Run("String method returns correct values", func(t *testing.T) {
		assert.Equal(t, "daily", CategoryDaily.String())
		assert.Equal(t, "community", CategoryCommunity.String())
		assert.Equal(t, "trading", CategoryTrading.String())
	})

	t.Run("IsValid returns correct values", func(t *testing.T) {
		assert.True(t, CategoryDaily.IsValid())
		assert.True(t, CategoryCommunity.IsValid())
		assert.True(t, CategoryTrading.IsValid())
		assert.False(t, TaskCategoryName("invalid").IsValid())
		assert.False(t, TaskCategoryName("").IsValid())
	})

	t.Run("GetDisplayName returns correct values", func(t *testing.T) {
		assert.Equal(t, "Daily Tasks", CategoryDaily.GetDisplayName())
		assert.Equal(t, "Community Tasks", CategoryCommunity.GetDisplayName())
		assert.Equal(t, "Trading Tasks", CategoryTrading.GetDisplayName())
		assert.Equal(t, "invalid", TaskCategoryName("invalid").GetDisplayName())
	})

	t.Run("GetDescription returns correct values", func(t *testing.T) {
		assert.Equal(t, "Complete these tasks daily to earn points", CategoryDaily.GetDescription())
		assert.Equal(t, "Engage with our community to earn rewards", CategoryCommunity.GetDescription())
		assert.Equal(t, "Complete trading activities to earn points", CategoryTrading.GetDescription())
		assert.Equal(t, "", TaskCategoryName("invalid").GetDescription())
	})

	t.Run("GetIcon returns correct values", func(t *testing.T) {
		assert.Equal(t, "calendar", CategoryDaily.GetIcon())
		assert.Equal(t, "users", CategoryCommunity.GetIcon())
		assert.Equal(t, "trending-up", CategoryTrading.GetIcon())
		assert.Equal(t, "", TaskCategoryName("invalid").GetIcon())
	})

	t.Run("GetSortOrder returns correct values", func(t *testing.T) {
		assert.Equal(t, 1, CategoryDaily.GetSortOrder())
		assert.Equal(t, 2, CategoryCommunity.GetSortOrder())
		assert.Equal(t, 3, CategoryTrading.GetSortOrder())
		assert.Equal(t, 999, TaskCategoryName("invalid").GetSortOrder())
	})

	t.Run("AllCategories returns all valid categories", func(t *testing.T) {
		categories := AllCategories()
		assert.Len(t, categories, 3)
		assert.Contains(t, categories, CategoryDaily)
		assert.Contains(t, categories, CategoryCommunity)
		assert.Contains(t, categories, CategoryTrading)
	})

	t.Run("TaskCategoryType alias works correctly", func(t *testing.T) {
		var categoryType TaskCategoryType = CategoryDaily
		assert.Equal(t, "daily", categoryType.String())
		assert.True(t, categoryType.IsValid())
	})
}

func TestTaskCategoryNameDatabaseOperations(t *testing.T) {
	t.Run("Value method returns correct driver value", func(t *testing.T) {
		value, err := CategoryDaily.Value()
		assert.NoError(t, err)
		assert.Equal(t, "daily", value)
	})

	t.Run("Scan method handles string values", func(t *testing.T) {
		var category TaskCategoryName
		err := category.Scan("community")
		assert.NoError(t, err)
		assert.Equal(t, CategoryCommunity, category)
	})

	t.Run("Scan method handles byte slice values", func(t *testing.T) {
		var category TaskCategoryName
		err := category.Scan([]byte("trading"))
		assert.NoError(t, err)
		assert.Equal(t, CategoryTrading, category)
	})

	t.Run("Scan method handles nil values", func(t *testing.T) {
		var category TaskCategoryName
		err := category.Scan(nil)
		assert.NoError(t, err)
		assert.Equal(t, TaskCategoryName(""), category)
	})

	t.Run("Scan method returns error for invalid types", func(t *testing.T) {
		var category TaskCategoryName
		err := category.Scan(123)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "cannot scan")
	})
}

func TestTaskCategoryNameConstants(t *testing.T) {
	t.Run("Constants have expected string values", func(t *testing.T) {
		assert.Equal(t, "daily", string(CategoryDaily))
		assert.Equal(t, "community", string(CategoryCommunity))
		assert.Equal(t, "trading", string(CategoryTrading))
	})

	t.Run("Constants are unique", func(t *testing.T) {
		categories := []TaskCategoryName{CategoryDaily, CategoryCommunity, CategoryTrading}
		seen := make(map[TaskCategoryName]bool)
		
		for _, category := range categories {
			assert.False(t, seen[category], "Duplicate category found: %s", category)
			seen[category] = true
		}
	})
}
