package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.78

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/resolvers"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// CompleteTask is the resolver for the completeTask field.
func (r *mutationResolver) CompleteTask(ctx context.Context, input gql_model.CompleteTaskInput) (*gql_model.TaskCompletionResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.CompleteTask(ctx, input)
}

// ClaimTaskReward is the resolver for the claimTaskReward field.
func (r *mutationResolver) ClaimTaskReward(ctx context.Context, input gql_model.ClaimTaskRewardInput) (*gql_model.TaskClaimResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.ClaimTaskReward(ctx, input)
}

// ClaimCashback is the resolver for the claimCashback field.
func (r *mutationResolver) ClaimCashback(ctx context.Context, input gql_model.ClaimCashbackInput) (*gql_model.CashbackClaimResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.ClaimCashback(ctx, input)
}

// RefreshTaskList is the resolver for the refreshTaskList field.
func (r *mutationResolver) RefreshTaskList(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.RefreshTaskList(ctx)
}

// ActivityCashbackDashboard is the resolver for the activityCashbackDashboard field.
func (r *queryResolver) ActivityCashbackDashboard(ctx context.Context) (*gql_model.UserDashboardResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.ActivityCashbackDashboard(ctx)
}

// ActivityCashbackSummary is the resolver for the activityCashbackSummary field.
func (r *queryResolver) ActivityCashbackSummary(ctx context.Context) (*gql_model.ActivityCashbackSummaryResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.ActivityCashbackSummary(ctx)
}

// TaskCenter is the resolver for the taskCenter field.
func (r *queryResolver) TaskCenter(ctx context.Context) (*gql_model.TaskCenterResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.TaskCenter(ctx)
}

// TierBenefits is the resolver for the tierBenefits field.
func (r *queryResolver) TierBenefits(ctx context.Context) (*gql_model.TierBenefitsResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.TierBenefits(ctx)
}

// UserTaskProgress is the resolver for the userTaskProgress field.
func (r *queryResolver) UserTaskProgress(ctx context.Context) (*gql_model.UserTaskProgressResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.UserTaskProgress(ctx)
}

// TaskCompletionHistory is the resolver for the taskCompletionHistory field.
func (r *queryResolver) TaskCompletionHistory(ctx context.Context, input *gql_model.TaskCompletionHistoryInput) (*gql_model.TaskCompletionHistoryResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.TaskCompletionHistory(ctx, input)
}

// UserTierInfo is the resolver for the userTierInfo field.
func (r *queryResolver) UserTierInfo(ctx context.Context) (*gql_model.UserTierInfo, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.UserTierInfo(ctx)
}

// TaskCategories is the resolver for the taskCategories field.
func (r *queryResolver) TaskCategories(ctx context.Context) ([]*gql_model.TaskCategory, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.TaskCategories(ctx)
}

// TasksByCategory is the resolver for the tasksByCategory field.
func (r *queryResolver) TasksByCategory(ctx context.Context, categoryName gql_model.TaskCategoryName) ([]*gql_model.ActivityTask, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.TasksByCategory(ctx, model.TaskCategoryName(categoryName))
}

// UserTaskListByCategory is the resolver for the userTaskListByCategory field.
func (r *queryResolver) UserTaskListByCategory(ctx context.Context, input gql_model.UserTaskListByCategoryInput) (*gql_model.UserTaskListByCategoryResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.UserTaskListByCategory(ctx, input)
}
