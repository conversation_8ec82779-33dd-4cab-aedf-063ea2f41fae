package activity_cashback

import (
	"context"
	"fmt"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"go.uber.org/zap"
)

// TaskSeeder seeds initial tasks into the database
type TaskSeeder struct {
	categoryRepo activity_cashback.TaskCategoryRepositoryInterface
	taskRepo     activity_cashback.ActivityTaskRepositoryInterface
}

// NewTaskSeeder creates a new TaskSeeder
func NewTaskSeeder() *TaskSeeder {
	return &TaskSeeder{
		categoryRepo: activity_cashback.NewTaskCategoryRepository(),
		taskRepo:     activity_cashback.NewActivityTaskRepository(),
	}
}

// SeedTasks seeds all initial tasks
func (s *TaskSeeder) SeedTasks(ctx context.Context) error {
	global.GVA_LOG.Info("Starting task seeding")

	// Get categories
	categories, err := s.categoryRepo.GetAll(ctx)
	if err != nil {
		return err
	}

	categoryMap := make(map[string]uint)
	for _, category := range categories {
		categoryMap[string(category.Name)] = category.ID
	}

	// Seed daily tasks
	if err := s.seedDailyTasks(ctx, categoryMap["daily"]); err != nil {
		return err
	}

	// Seed community tasks
	if err := s.seedCommunityTasks(ctx, categoryMap["community"]); err != nil {
		return err
	}

	// Seed trading tasks
	if err := s.seedTradingTasks(ctx, categoryMap["trading"]); err != nil {
		return err
	}

	global.GVA_LOG.Info("Task seeding completed successfully")
	return nil
}

// seedDailyTasks seeds daily tasks
func (s *TaskSeeder) seedDailyTasks(ctx context.Context, categoryID uint) error {
	dailyTasks := []struct {
		name               string
		description        string
		taskIdentifier     model.TaskIdentifier
		points             int
		taskType           model.TaskType
		frequency          model.TaskFrequency
		resetPeriod        *model.ResetPeriod
		conditions         *model.TaskConditions
		actionTarget       *string
		verificationMethod *model.VerificationMethod
		taskIcon           *string
		buttonText         *string
		sortOrder          int
	}{
		{
			name:               "Daily Check-in",
			description:        "Check in daily to earn points",
			taskIdentifier:     model.TaskIDDailyCheckin,
			points:             5,
			taskType:           model.TaskTypeDaily,
			frequency:          model.FrequencyDaily,
			taskIcon:           &[]string{"📅"}[0],
			buttonText:         &[]string{"Go to view"}[0],
			resetPeriod:        &[]model.ResetPeriod{model.ResetDaily}[0],
			actionTarget:       &[]string{"Homepage"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          1,
		},
		{
			name:               "Complete one meme trade",
			description:        "Complete one meme trade",
			taskIdentifier:     model.TaskIDMemeTradeDaily,
			points:             200,
			taskType:           model.TaskTypeDaily,
			frequency:          model.FrequencyDaily,
			taskIcon:           &[]string{"💰"}[0],
			buttonText:         &[]string{"trade"}[0],
			resetPeriod:        &[]model.ResetPeriod{model.ResetDaily}[0],
			conditions:         &model.TaskConditions{RequiredTradeCount: &[]int{1}[0]},
			actionTarget:       &[]string{"MEME homepage"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          2,
		},
		{
			name:               "Complete one derivatives trade",
			description:        "Complete one derivatives trade",
			taskIdentifier:     model.TaskIDPerpetualTradeDaily,
			points:             200,
			taskType:           model.TaskTypeDaily,
			frequency:          model.FrequencyDaily,
			taskIcon:           &[]string{"💰"}[0],
			buttonText:         &[]string{"trade"}[0],
			resetPeriod:        &[]model.ResetPeriod{model.ResetDaily}[0],
			conditions:         &model.TaskConditions{RequiredTradeCount: &[]int{1}[0]},
			actionTarget:       &[]string{"Derivatives homepage"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          3,
		},
		{
			name:               "View market page",
			description:        "View the market page",
			taskIdentifier:     model.TaskIDMarketPageView,
			points:             5,
			taskType:           model.TaskTypeDaily,
			frequency:          model.FrequencyDaily,
			taskIcon:           &[]string{"👀"}[0],
			buttonText:         &[]string{"view"}[0],
			resetPeriod:        &[]model.ResetPeriod{model.ResetDaily}[0],
			actionTarget:       &[]string{"Market homepage"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          4,
		},
		{
			name:               "Consecutive Check-in (3/7/30 days)",
			description:        "Check in consecutively to earn bonus points",
			taskIdentifier:     model.TaskIDConsecutiveCheckin,
			points:             0, // Points awarded based on streak
			taskType:           model.TaskTypeDaily,
			frequency:          model.FrequencyProgressive,
			taskIcon:           &[]string{"📅"}[0],
			buttonText:         &[]string{"Completed"}[0],
			conditions:         &model.TaskConditions{ConsecutiveDays: &[]int{30}[0]},
			actionTarget:       &[]string{"Homepage"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          5,
		},
		{
			name:               "Trade for 3/7/15/30 consecutive days",
			description:        "Trade continuously for N days",
			taskIdentifier:     model.TaskIDConsecutiveTradingDays,
			points:             0, // Points awarded based on streak: 50/200/1000/2000
			taskType:           model.TaskTypeTrading,
			frequency:          model.FrequencyProgressive,
			taskIcon:           &[]string{"💰"}[0],
			buttonText:         &[]string{"Go to trade"}[0],
			conditions:         &model.TaskConditions{ConsecutiveDays: &[]int{30}[0]},
			actionTarget:       &[]string{"Trading page"}[0],
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			sortOrder:          6,
		},
	}

	for _, taskData := range dailyTasks {
		if err := s.createTaskFromDefinition(ctx, categoryID, taskData.taskIdentifier, taskData.taskType, taskData.frequency, taskData.resetPeriod, taskData.conditions, taskData.actionTarget, taskData.verificationMethod, nil, taskData.sortOrder); err != nil {
			return err
		}

		// Update task with icon and button text if provided
		if taskData.taskIcon != nil || taskData.buttonText != nil {
			if err := s.updateTaskIconAndButtonText(ctx, categoryID, taskData.taskIdentifier, taskData.taskIcon, taskData.buttonText); err != nil {
				return err
			}
		}
	}

	return nil
}

// seedCommunityTasks seeds community tasks
func (s *TaskSeeder) seedCommunityTasks(ctx context.Context, categoryID uint) error {
	communityTasks := []struct {
		name               string
		description        string
		points             int
		taskType           model.TaskType
		frequency          model.TaskFrequency
		verificationMethod *model.VerificationMethod
		externalLink       *string
		taskIcon           *string
		buttonText         *string
		sortOrder          int
	}{
		{
			name:               "Follow on X (Twitter)",
			description:        "Follow the project's official Twitter",
			points:             50,
			taskType:           model.TaskTypeCommunity,
			frequency:          model.FrequencyOneTime,
			verificationMethod: &[]model.VerificationMethod{model.VerificationClickVerify}[0],
			externalLink:       &[]string{"https://x.com/intent/follow?screen_name=XBITDEX_EN"}[0],
			taskIcon:           &[]string{"🐦"}[0],
			buttonText:         &[]string{"Go to follow"}[0],
			sortOrder:          1,
		},
		{
			name:               "Retweet a post",
			description:        "Retweet project posts",
			points:             10,
			taskType:           model.TaskTypeCommunity,
			frequency:          model.FrequencyUnlimited,
			verificationMethod: &[]model.VerificationMethod{model.VerificationClickVerify}[0],
			externalLink:       &[]string{"https://x.com/intent/retweet?tweet_id=1952241291666035139"}[0],
			taskIcon:           &[]string{"🔄"}[0],
			buttonText:         &[]string{"Go to share"}[0],
			sortOrder:          2,
		},
		{
			name:               "Like a post",
			description:        "Like project posts",
			points:             10,
			taskType:           model.TaskTypeCommunity,
			frequency:          model.FrequencyUnlimited,
			verificationMethod: &[]model.VerificationMethod{model.VerificationClickVerify}[0],
			externalLink:       &[]string{"https://x.com/intent/like?tweet_id=1952241291666035139"}[0],
			taskIcon:           &[]string{"❤️"}[0],
			buttonText:         &[]string{"Go to view"}[0],
			sortOrder:          3,
		},
		{
			name:               "Join Telegram",
			description:        "Join project's Telegram group",
			points:             30,
			taskType:           model.TaskTypeCommunity,
			frequency:          model.FrequencyOneTime,
			verificationMethod: &[]model.VerificationMethod{model.VerificationClickVerify}[0],
			externalLink:       &[]string{"https://t.me/xbit_dex"}[0],
			taskIcon:           &[]string{"📱"}[0],
			buttonText:         &[]string{"Go to view"}[0],
			sortOrder:          4,
		},
		{
			name:               "Invite friends",
			description:        "Invite friends to join the platform",
			points:             100,
			taskType:           model.TaskTypeCommunity,
			frequency:          model.FrequencyUnlimited,
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			taskIcon:           &[]string{"👥"}[0],
			buttonText:         &[]string{"Go to share"}[0],
			sortOrder:          5,
		},
		{
			name:               "Share referral link",
			description:        "Share referral link",
			points:             10,
			taskType:           model.TaskTypeCommunity,
			frequency:          model.FrequencyDaily,
			verificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
			taskIcon:           &[]string{"🔗"}[0],
			buttonText:         &[]string{"Go to forward"}[0],
			sortOrder:          6,
		},
	}

	for _, taskData := range communityTasks {
		resetPeriod := model.ResetDaily
		if taskData.frequency == model.FrequencyDaily {
			resetPeriod = model.ResetDaily
		} else {
			resetPeriod = model.ResetNever
		}

		if err := s.createTaskIfNotExistsWithIcon(ctx, categoryID, taskData.name, taskData.description, nil, taskData.points, taskData.taskType, taskData.frequency, &resetPeriod, nil, nil, taskData.verificationMethod, taskData.externalLink, taskData.taskIcon, taskData.buttonText, taskData.sortOrder); err != nil {
			return err
		}
	}

	return nil
}

// seedTradingTasks seeds trading tasks
func (s *TaskSeeder) seedTradingTasks(ctx context.Context, categoryID uint) error {
	tradingTasks := []struct {
		name        string
		description string
		points      int
		taskType    model.TaskType
		frequency   model.TaskFrequency
		conditions  *model.TaskConditions
		sortOrder   int
	}{
		{
			name:        "Trading Points",
			description: "Points based on daily trading volume",
			points:      0, // Variable points based on volume
			taskType:    model.TaskTypeTrading,
			frequency:   model.FrequencyDaily,
			sortOrder:   1,
		},
		{
			name:        "Cumulative trading $10,000",
			description: "Achieve total trading volume of $10,000",
			points:      300,
			taskType:    model.TaskTypeTrading,
			frequency:   model.FrequencyProgressive,
			conditions:  &model.TaskConditions{MinTradingVolume: &[]float64{10000}[0]},
			sortOrder:   2,
		},
		{
			name:        "Cumulative trading $50,000",
			description: "Achieve total trading volume of $50,000",
			points:      1000,
			taskType:    model.TaskTypeTrading,
			frequency:   model.FrequencyProgressive,
			conditions:  &model.TaskConditions{MinTradingVolume: &[]float64{50000}[0]},
			sortOrder:   3,
		},
		{
			name:        "Cumulative trading $100,000",
			description: "Achieve total trading volume of $100,000",
			points:      2500,
			taskType:    model.TaskTypeTrading,
			frequency:   model.FrequencyProgressive,
			conditions:  &model.TaskConditions{MinTradingVolume: &[]float64{100000}[0]},
			sortOrder:   4,
		},
		{
			name:        "Cumulative trading $500,000",
			description: "Achieve total trading volume of $500,000",
			points:      10000,
			taskType:    model.TaskTypeTrading,
			frequency:   model.FrequencyProgressive,
			conditions:  &model.TaskConditions{MinTradingVolume: &[]float64{500000}[0]},
			sortOrder:   5,
		},
	}

	for _, taskData := range tradingTasks {
		resetPeriod := model.ResetNever
		verificationMethod := model.VerificationAuto
		actionTarget := "Trading history"

		// Trading Points task should reset daily
		if taskData.name == "Trading Points" {
			resetPeriod = model.ResetDaily
		}

		if err := s.createTaskIfNotExists(ctx, categoryID, taskData.name, taskData.description, nil, taskData.points, taskData.taskType, taskData.frequency, &resetPeriod, taskData.conditions, &actionTarget, &verificationMethod, nil, taskData.sortOrder); err != nil {
			return err
		}
	}

	return nil
}

// createTaskFromDefinition creates a task from its definition if it doesn't already exist
func (s *TaskSeeder) createTaskFromDefinition(ctx context.Context, categoryID uint, identifier model.TaskIdentifier, taskType model.TaskType, frequency model.TaskFrequency, resetPeriod *model.ResetPeriod, conditions *model.TaskConditions, actionTarget *string, verificationMethod *model.VerificationMethod, externalLink *string, sortOrder int) error {
	// Get task definition
	definition, exists := model.GetTaskDefinition(identifier)
	if !exists {
		return fmt.Errorf("task definition not found: %s", identifier)
	}

	// Check if task already exists by identifier
	tasks, err := s.taskRepo.GetByCategoryID(ctx, categoryID)
	if err != nil {
		return fmt.Errorf("failed to get tasks by category: %w", err)
	}

	// Check if task with this identifier already exists
	for _, task := range tasks {
		if task.TaskIdentifier != nil && *task.TaskIdentifier == identifier {
			// Task already exists, skip creation
			return nil
		}
	}

	// Create new task from definition
	task := &model.ActivityTask{
		CategoryID:         categoryID,
		Name:               definition.DisplayName,
		Description:        &definition.Description,
		TaskType:           taskType,
		Frequency:          frequency,
		TaskIdentifier:     &identifier,
		Points:             definition.Points,
		ResetPeriod:        resetPeriod,
		Conditions:         conditions,
		ActionTarget:       actionTarget,
		VerificationMethod: verificationMethod,
		ExternalLink:       externalLink,
		IsActive:           true,
		SortOrder:          sortOrder,
	}

	if err := s.taskRepo.Create(ctx, task); err != nil {
		return fmt.Errorf("failed to create task %s: %w", identifier, err)
	}

	global.GVA_LOG.Info("Task created successfully",
		zap.String("identifier", string(identifier)),
		zap.String("name", definition.DisplayName))
	return nil
}

// createTaskIfNotExists creates a task if it doesn't already exist (legacy method)
func (s *TaskSeeder) createTaskIfNotExists(ctx context.Context, categoryID uint, name, description string, taskIdentifier *model.TaskIdentifier, points int, taskType model.TaskType, frequency model.TaskFrequency, resetPeriod *model.ResetPeriod, conditions *model.TaskConditions, actionTarget *string, verificationMethod *model.VerificationMethod, externalLink *string, sortOrder int) error {
	// Check if task already exists
	tasks, err := s.taskRepo.GetByCategoryID(ctx, categoryID)
	if err != nil {
		return err
	}

	for _, task := range tasks {
		if task.Name == name {
			global.GVA_LOG.Debug("Task already exists, skipping", zap.String("name", name))
			return nil
		}
	}

	// Create new task
	task := &model.ActivityTask{
		CategoryID:         categoryID,
		Name:               name,
		Description:        &description,
		TaskType:           taskType,
		Frequency:          frequency,
		TaskIdentifier:     taskIdentifier,
		Points:             points,
		ResetPeriod:        resetPeriod,
		Conditions:         conditions,
		ActionTarget:       actionTarget,
		VerificationMethod: verificationMethod,
		ExternalLink:       externalLink,
		IsActive:           true,
		SortOrder:          sortOrder,
	}

	if err := s.taskRepo.Create(ctx, task); err != nil {
		global.GVA_LOG.Error("Failed to create task", zap.Error(err), zap.String("name", name))
		return err
	}

	global.GVA_LOG.Info("Task created successfully", zap.String("name", name), zap.String("task_id", task.ID.String()))
	return nil
}

// updateTaskIconAndButtonText updates task icon and button text for existing tasks
func (s *TaskSeeder) updateTaskIconAndButtonText(ctx context.Context, categoryID uint, identifier model.TaskIdentifier, taskIcon *string, buttonText *string) error {
	// Get tasks by category
	tasks, err := s.taskRepo.GetByCategoryID(ctx, categoryID)
	if err != nil {
		return fmt.Errorf("failed to get tasks by category: %w", err)
	}

	// Find task with matching identifier
	for _, task := range tasks {
		if task.TaskIdentifier != nil && *task.TaskIdentifier == identifier {
			// Update icon and button text
			if taskIcon != nil {
				task.TaskIcon = taskIcon
			}
			if buttonText != nil {
				task.ButtonText = buttonText
			}

			// Update the task
			if err := s.taskRepo.Update(ctx, &task); err != nil {
				return fmt.Errorf("failed to update task %s: %w", identifier, err)
			}

			global.GVA_LOG.Info("Task icon and button text updated successfully",
				zap.String("identifier", string(identifier)),
				zap.String("name", task.Name))
			return nil
		}
	}

	return fmt.Errorf("task with identifier %s not found", identifier)
}

// createTaskIfNotExistsWithIcon creates a task if it doesn't already exist with icon and button text support
func (s *TaskSeeder) createTaskIfNotExistsWithIcon(ctx context.Context, categoryID uint, name, description string, taskIdentifier *model.TaskIdentifier, points int, taskType model.TaskType, frequency model.TaskFrequency, resetPeriod *model.ResetPeriod, conditions *model.TaskConditions, actionTarget *string, verificationMethod *model.VerificationMethod, externalLink *string, taskIcon *string, buttonText *string, sortOrder int) error {
	// Check if task already exists
	tasks, err := s.taskRepo.GetByCategoryID(ctx, categoryID)
	if err != nil {
		return err
	}

	for _, task := range tasks {
		if task.Name == name {
			global.GVA_LOG.Debug("Task already exists, skipping", zap.String("name", name))
			return nil
		}
	}

	// Create new task
	task := &model.ActivityTask{
		CategoryID:         categoryID,
		Name:               name,
		Description:        &description,
		TaskType:           taskType,
		Frequency:          frequency,
		TaskIdentifier:     taskIdentifier,
		Points:             points,
		ResetPeriod:        resetPeriod,
		Conditions:         conditions,
		ActionTarget:       actionTarget,
		VerificationMethod: verificationMethod,
		ExternalLink:       externalLink,
		TaskIcon:           taskIcon,
		ButtonText:         buttonText,
		IsActive:           true,
		SortOrder:          sortOrder,
	}

	if err := s.taskRepo.Create(ctx, task); err != nil {
		global.GVA_LOG.Error("Failed to create task", zap.Error(err), zap.String("name", name))
		return err
	}

	global.GVA_LOG.Info("Task created successfully", zap.String("name", name), zap.String("task_id", task.ID.String()))
	return nil
}
